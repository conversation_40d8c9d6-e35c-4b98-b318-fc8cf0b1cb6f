{"name": "my-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@fortawesome/fontawesome-free": "^6.7.2", "aos": "^2.3.4", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "6.22.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1"}}