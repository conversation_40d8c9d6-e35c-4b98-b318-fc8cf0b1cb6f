import { useState, useRef, useEffect } from 'react';

const ChatBot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: 'Hi there! 👋 I\'m your TrypIndia assistant. How can I help you plan your perfect North East India adventure today?',
      sender: 'bot',
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [messages, isOpen]);

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!inputValue.trim()) return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      text: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages([...messages, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate bot response after a delay
    setTimeout(() => {
      const botResponse = getBotResponse(inputValue);
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: prevMessages.length + 1,
          text: botResponse,
          sender: 'bot',
          timestamp: new Date(),
        },
      ]);
      setIsTyping(false);
    }, 1000);
  };

  // Simple response logic - in a real app, this would be more sophisticated
  const getBotResponse = (userInput) => {
    const input = userInput.toLowerCase();

    if (input.includes('hello') || input.includes('hi') || input.includes('hey')) {
      return 'Hello! How can I help you plan your trip to North East India?';
    } else if (input.includes('darjeeling')) {
      return 'Darjeeling is famous for its tea gardens, toy train, and views of Mt. Kanchenjunga. The best time to visit is between March-May and September-November. Would you like to know about our Darjeeling packages?';
    } else if (input.includes('gangtok') || input.includes('sikkim')) {
      return 'Gangtok is the capital of Sikkim with beautiful monasteries and mountain views. North Sikkim offers stunning landscapes like Yumthang Valley and Gurudongmar Lake. Would you like more details about our Sikkim tours?';
    } else if (input.includes('package') || input.includes('tour') || input.includes('price')) {
      return 'We offer various packages starting from ₹15,000 per person. This includes accommodation, transportation, and guided tours. Would you like me to send you our detailed package information?';
    } else if (input.includes('book') || input.includes('reservation')) {
      return 'Great! To book a tour, you can either fill out the form on our Contact page or call us at +91 8584807189. Would you like me to guide you to the booking form?';
    } else if (input.includes('weather') || input.includes('best time')) {
      return 'The best time to visit North East India is during March-May (spring) and September-November (autumn) when the weather is pleasant. Winters (December-February) can be very cold, while monsoon (June-August) sees heavy rainfall.';
    } else if (input.includes('thank')) {
      return 'You\'re welcome! Feel free to ask if you have any other questions. Happy travels!';
    } else {
      return 'Thank you for your message. For more specific information about our tours and packages, please contact us <NAME_EMAIL> or call +91 8584807189. Would you like to know about any specific destination?';
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <>
      {/* Chat Button */}
      <button
        onClick={toggleChat}
        className={`fixed bottom-6 right-6 z-50 bg-gradient-to-r from-teal-500 to-blue-600 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 ${
          isOpen ? 'scale-0 opacity-0' : 'scale-100 opacity-100'
        }`}
        aria-label="Open chat"
      >
        <i className="fas fa-comments text-xl"></i>
      </button>

      {/* Chat Window */}
      <div
        className={`fixed bottom-6 right-6 z-50 bg-white rounded-xl shadow-2xl w-full max-w-sm overflow-hidden transition-all duration-300 transform ${
          isOpen ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
        }`}
        style={{ maxHeight: '550px' }}
      >
        {/* Chat Header */}
        <div className="bg-gradient-to-r from-teal-500 to-blue-600 p-3 flex justify-between items-center">
          <div className="flex items-center">
            <div className="bg-white rounded-full w-9 h-9 flex items-center justify-center mr-3 shadow-md">
              <i className="fas fa-robot text-teal-500"></i>
            </div>
            <div>
              <h3 className="text-white font-bold">TrypIndia Assistant</h3>
              <p className="text-blue-100 text-xs">Online | Ask me anything</p>
            </div>
          </div>
          <button
            onClick={toggleChat}
            className="text-white hover:text-blue-200 transition-colors"
            aria-label="Close chat"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>

        {/* Chat Messages */}
        <div className="p-3 h-80 overflow-y-auto bg-gray-50">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`mb-3 flex ${
                message.sender === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 shadow-sm ${
                  message.sender === 'user'
                    ? 'bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-tr-none'
                    : 'bg-blue-50 border border-blue-100 rounded-tl-none'
                }`}
              >
                <p className="text-sm">{message.text}</p>
                <p
                  className={`text-xs mt-1 ${
                    message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                  }`}
                >
                  {formatTime(message.timestamp)}
                </p>
              </div>
            </div>
          ))}

          {isTyping && (
            <div className="flex justify-start mb-3">
              <div className="bg-blue-50 border border-blue-100 rounded-lg rounded-tl-none p-3 max-w-[80%] shadow-sm">
                <div className="flex space-x-1">
                  <div className="bg-teal-400 rounded-full w-2 h-2 animate-pulse"></div>
                  <div className="bg-teal-400 rounded-full w-2 h-2 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  <div className="bg-teal-400 rounded-full w-2 h-2 animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Chat Input */}
        <form onSubmit={handleSubmit} className="border-t border-gray-200 p-3 bg-white">
          <div className="flex">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              placeholder="Type your message..."
              className="flex-1 border border-gray-300 rounded-l-full py-2 px-4 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm"
            />
            <button
              type="submit"
              className="bg-gradient-to-r from-teal-500 to-blue-600 text-white rounded-r-full px-4 hover:shadow-md transition-all duration-300"
              disabled={!inputValue.trim()}
              aria-label="Send message"
            >
              <i className="fas fa-paper-plane"></i>
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default ChatBot;
