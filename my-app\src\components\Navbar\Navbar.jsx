import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Navbar.css';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const location = useLocation();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Handle mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  return (
    <>
      {/* Animated background gradient */}
      <div className="fixed top-0 left-0 w-full h-20 z-40 pointer-events-none">
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(99, 102, 241, 0.15), transparent 40%)`
          }}
        />
      </div>

      <nav
        className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ease-out ${
          scrolled
            ? 'bg-white/95 backdrop-blur-xl shadow-2xl shadow-indigo-500/10 py-3 border-b border-indigo-100/50'
            : 'bg-gradient-to-r from-slate-900/90 via-indigo-900/90 to-purple-900/90 backdrop-blur-2xl py-5 border-b border-white/10'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            {/* Logo with animated elements */}
            <Link to="/" className="flex items-center group">
              <div className={`flex items-center font-bold transition-all duration-300 ${scrolled ? 'text-indigo-600' : 'text-white'}`}>
                <div className="relative mr-3">
                  <div className={`absolute inset-0 rounded-full transition-all duration-300 ${
                    scrolled ? 'bg-indigo-100' : 'bg-white/20'
                  } group-hover:scale-110 group-hover:rotate-12`} />
                  <i className="fas fa-mountain text-xl relative z-10 p-2 transition-transform duration-300 group-hover:scale-110"></i>
                </div>
                <span className="text-2xl font-bold tracking-tight bg-gradient-to-r from-current to-current bg-clip-text group-hover:from-indigo-400 group-hover:to-purple-400 transition-all duration-300">
                  TrypIndia
                </span>
              </div>
            </Link>

          {/* Desktop Navigation with floating glass effect */}
          <div className="hidden md:flex items-center">
            <div className={`flex items-center space-x-2 px-4 py-2 rounded-full transition-all duration-500 ${
              scrolled
                ? 'bg-white/80 backdrop-blur-lg shadow-lg shadow-indigo-500/20 border border-indigo-100/50'
                : 'bg-white/10 backdrop-blur-lg border border-white/20'
            }`}>
              {[
                { name: 'Home', path: '/' },
                { name: 'Plan', path: '/plan' },
                { name: 'About', path: '/about' },
                { name: 'Gallery', path: '/gallery' },
                { name: 'Contact', path: '/contact' },
              ].map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`relative px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 group overflow-hidden ${
                    location.pathname === item.path
                      ? scrolled
                        ? 'text-white bg-gradient-to-r from-indigo-600 to-purple-600 shadow-lg shadow-indigo-500/30'
                        : 'text-white bg-gradient-to-r from-indigo-500 to-purple-500 shadow-lg shadow-white/20'
                      : scrolled
                        ? 'text-slate-700 hover:text-white hover:bg-gradient-to-r hover:from-indigo-500 hover:to-purple-500'
                        : 'text-white/90 hover:text-white hover:bg-white/20'
                  }`}
                >
                  <div className="flex items-center relative z-10">
                    <span className="relative">
                      {item.name}
                      <div className={`absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-400 to-purple-400 transition-all duration-300 group-hover:w-full ${
                        location.pathname === item.path ? 'w-full' : ''
                      }`} />
                    </span>
                  </div>
                  {/* Hover effect background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full" />
                </Link>
              ))}
            </div>
          </div>

          {/* Premium Login Button */}
          <div className="hidden md:flex items-center ml-6">
            <Link
              to="/login"
              className={`relative px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 group overflow-hidden ${
                scrolled
                  ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/30 hover:shadow-xl hover:shadow-indigo-500/40 hover:scale-105'
                  : 'bg-white text-indigo-700 shadow-lg shadow-white/20 hover:shadow-xl hover:shadow-white/30 hover:scale-105'
              }`}
            >
              <div className="flex items-center relative z-10">
                <i className="fas fa-user-circle mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                <span>Login</span>
              </div>
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full" />
            </Link>
          </div>

          {/* Stylish Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className={`relative p-3 rounded-full transition-all duration-300 group ${
                scrolled
                  ? 'text-slate-700 bg-white/80 backdrop-blur-lg shadow-lg shadow-indigo-500/20 border border-indigo-100/50 hover:bg-indigo-50'
                  : 'text-white bg-white/10 backdrop-blur-lg border border-white/20 hover:bg-white/20'
              } focus:outline-none`}
              aria-label="Toggle menu"
            >
              <div className="relative w-6 h-6 flex items-center justify-center">
                <div className={`absolute transition-all duration-300 ${isOpen ? 'rotate-45 translate-y-0' : 'rotate-0 -translate-y-2'}`}>
                  <div className={`w-6 h-0.5 rounded-full transition-all duration-300 ${scrolled ? 'bg-slate-700' : 'bg-white'}`} />
                </div>
                <div className={`absolute transition-all duration-300 ${isOpen ? 'opacity-0' : 'opacity-100'}`}>
                  <div className={`w-6 h-0.5 rounded-full transition-all duration-300 ${scrolled ? 'bg-slate-700' : 'bg-white'}`} />
                </div>
                <div className={`absolute transition-all duration-300 ${isOpen ? '-rotate-45 translate-y-0' : 'rotate-0 translate-y-2'}`}>
                  <div className={`w-6 h-0.5 rounded-full transition-all duration-300 ${scrolled ? 'bg-slate-700' : 'bg-white'}`} />
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Premium Mobile Menu */}
      <div
        className={`md:hidden transition-all duration-500 ease-out overflow-hidden ${
          isOpen ? 'max-h-[600px] opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className={`mx-4 my-4 rounded-2xl backdrop-blur-xl border transition-all duration-300 ${
          scrolled
            ? 'bg-white/95 border-indigo-100/50 shadow-xl shadow-indigo-500/10'
            : 'bg-white/10 border-white/20 shadow-xl shadow-black/20'
        }`}>
          <div className="p-6 space-y-3">
            {[
              { name: 'Home', path: '/' },
              { name: 'Plan Your Trip', path: '/plan' },
              { name: 'About', path: '/about' },
              { name: 'Gallery', path: '/gallery' },
              { name: 'Contact', path: '/contact' },
            ].map((item, index) => (
              <Link
                key={item.name}
                to={item.path}
                className={`block px-4 py-3 rounded-xl text-base font-medium transition-all duration-300 group ${
                  location.pathname === item.path
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/30'
                    : scrolled
                      ? 'text-slate-700 hover:bg-gradient-to-r hover:from-indigo-500 hover:to-purple-500 hover:text-white'
                      : 'text-white hover:bg-white/20'
                }`}
                style={{
                  animationDelay: `${index * 50}ms`,
                  animation: isOpen ? 'slideInFromRight 0.3s ease-out forwards' : 'none'
                }}
              >
                <div className="flex items-center justify-center">
                  <span>{item.name}</span>
                </div>
              </Link>
            ))}

            <div className="pt-4 border-t border-white/20">
              <Link
                to="/login"
                className={`block w-full text-center px-6 py-3 rounded-xl text-base font-semibold transition-all duration-300 group ${
                  scrolled
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/30 hover:shadow-xl hover:scale-105'
                    : 'bg-white text-indigo-700 shadow-lg shadow-white/20 hover:shadow-xl hover:scale-105'
                }`}
              >
                <div className="flex items-center justify-center">
                  <i className="fas fa-user-circle mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                  <span>Login / Sign Up</span>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
    </>
  );
};

export default Navbar;
