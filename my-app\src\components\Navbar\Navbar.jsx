import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  return (
    <nav
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        scrolled
          ? 'bg-white shadow-lg py-2'
          : 'bg-indigo-900/80 backdrop-blur-md py-4'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <div className={`flex items-center font-bold ${scrolled ? 'text-indigo-600' : 'text-white'}`}>
              <div className="mr-2">
                <i className="fas fa-mountain text-xl"></i>
              </div>
              <span className="text-xl font-bold tracking-tight">
                TrypIndia
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {[
              { name: 'Home', path: '/', icon: 'fas fa-home' },
              { name: 'Plan', path: '/plan', icon: 'fas fa-map-marked-alt' },
              { name: 'About', path: '/about', icon: 'fas fa-info-circle' },
              { name: 'Gallery', path: '/gallery', icon: 'fas fa-images' },
              { name: 'Contact', path: '/contact', icon: 'fas fa-envelope' },
            ].map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  location.pathname === item.path
                    ? scrolled
                      ? 'bg-indigo-100 text-indigo-700'
                      : 'bg-indigo-700 text-white'
                    : scrolled
                      ? 'text-gray-700 hover:bg-indigo-50 hover:text-indigo-700'
                      : 'text-white hover:bg-indigo-800 hover:text-white'
                }`}
              >
                <div className="flex items-center">
                  <i className={`${item.icon} mr-1 text-xs`}></i>
                  <span>{item.name}</span>
                </div>
              </Link>
            ))}
          </div>

          {/* Login Button */}
          <div className="hidden md:flex items-center">
            <Link
              to="/login"
              className={`ml-4 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                scrolled
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                  : 'bg-white text-indigo-700 hover:bg-indigo-50'
              }`}
            >
              <div className="flex items-center">
                <i className="fas fa-user-circle mr-1"></i>
                <span>Login</span>
              </div>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className={`inline-flex items-center justify-center p-2 rounded-md ${
                scrolled
                  ? 'text-gray-700 hover:text-indigo-600 hover:bg-indigo-50'
                  : 'text-white hover:text-white hover:bg-indigo-800'
              } focus:outline-none transition duration-150 ease-in-out`}
              aria-label="Toggle menu"
            >
              {isOpen ? (
                <i className="fas fa-times text-xl"></i>
              ) : (
                <i className="fas fa-bars text-xl"></i>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`md:hidden transition-all duration-300 ease-in-out overflow-hidden ${
          isOpen ? 'max-h-[500px]' : 'max-h-0'
        }`}
      >
        <div className={`px-2 pt-2 pb-3 space-y-1 sm:px-3 ${scrolled ? 'bg-white' : 'bg-indigo-900/90'}`}>
          {[
            { name: 'Home', path: '/', icon: 'fas fa-home' },
            { name: 'Plan Your Trip', path: '/plan', icon: 'fas fa-map-marked-alt' },
            { name: 'About', path: '/about', icon: 'fas fa-info-circle' },
            { name: 'Gallery', path: '/gallery', icon: 'fas fa-images' },
            { name: 'Contact', path: '/contact', icon: 'fas fa-envelope' },
          ].map((item) => (
            <Link
              key={item.name}
              to={item.path}
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                location.pathname === item.path
                  ? scrolled
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'bg-indigo-700 text-white'
                  : scrolled
                    ? 'text-gray-700 hover:bg-indigo-50 hover:text-indigo-700'
                    : 'text-white hover:bg-indigo-800 hover:text-white'
              }`}
            >
              <div className="flex items-center">
                <i className={`${item.icon} mr-2 w-5 text-center`}></i>
                <span>{item.name}</span>
              </div>
            </Link>
          ))}
          <div className="pt-4">
            <Link
              to="/login"
              className={`block w-full text-center px-4 py-2 rounded-md text-base font-medium ${
                scrolled
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                  : 'bg-white text-indigo-700 hover:bg-indigo-50'
              }`}
            >
              <div className="flex items-center justify-center">
                <i className="fas fa-user-circle mr-2"></i>
                <span>Login / Sign Up</span>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
