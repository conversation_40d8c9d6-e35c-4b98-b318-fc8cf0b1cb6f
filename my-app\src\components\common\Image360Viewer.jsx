import { useState, useRef, useEffect } from 'react';

const Image360Viewer = ({ images }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isRotating, setIsRotating] = useState(false);
  const [rotationSpeed, setRotationSpeed] = useState(3); // seconds per rotation
  const [showInfo, setShowInfo] = useState(false);

  const viewerRef = useRef(null);
  const scrollContainerRef = useRef(null);
  const rotationIntervalRef = useRef(null);

  // Handle image selection
  const handleImageClick = (index) => {
    setActiveIndex(index);
    stopRotation();
  };

  // Navigation handlers
  const handlePrev = () => {
    setActiveIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
    stopRotation();
  };

  const handleNext = () => {
    setActiveIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    stopRotation();
  };

  // Drag handlers for thumbnail scrolling
  const handleMouseDown = (e) => {
    if (scrollContainerRef.current) {
      setIsDragging(true);
      setStartX(e.pageX - scrollContainerRef.current.offsetLeft);
      setScrollLeft(scrollContainerRef.current.scrollLeft);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    e.preventDefault();
    if (scrollContainerRef.current) {
      const x = e.pageX - scrollContainerRef.current.offsetLeft;
      const walk = (x - startX) * 2; // Scroll speed multiplier
      scrollContainerRef.current.scrollLeft = scrollLeft - walk;
    }
  };

  // Fullscreen toggle
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (viewerRef.current.requestFullscreen) {
        viewerRef.current.requestFullscreen();
      } else if (viewerRef.current.webkitRequestFullscreen) {
        viewerRef.current.webkitRequestFullscreen();
      } else if (viewerRef.current.msRequestFullscreen) {
        viewerRef.current.msRequestFullscreen();
      }
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
      setIsFullscreen(false);
    }
  };

  // Auto-rotation functions
  const startRotation = () => {
    if (rotationIntervalRef.current) clearInterval(rotationIntervalRef.current);

    rotationIntervalRef.current = setInterval(() => {
      setActiveIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    }, rotationSpeed * 1000);

    setIsRotating(true);
  };

  const stopRotation = () => {
    if (rotationIntervalRef.current) {
      clearInterval(rotationIntervalRef.current);
      rotationIntervalRef.current = null;
    }
    setIsRotating(false);
  };

  const toggleRotation = () => {
    if (isRotating) {
      stopRotation();
    } else {
      startRotation();
    }
  };

  // Change rotation speed
  const changeRotationSpeed = (newSpeed) => {
    setRotationSpeed(newSpeed);
    if (isRotating) {
      stopRotation();
      startRotation();
    }
  };

  // Toggle info panel
  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  // Handle fullscreen change
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
      if (rotationIntervalRef.current) clearInterval(rotationIntervalRef.current);
    };
  }, []);

  // Scroll active thumbnail into view
  useEffect(() => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const activeThumb = container.querySelector(`.thumb-${activeIndex}`);

      if (activeThumb) {
        const containerWidth = container.offsetWidth;
        const thumbLeft = activeThumb.offsetLeft;
        const thumbWidth = activeThumb.offsetWidth;

        // Calculate the center position for the active thumbnail
        const scrollPosition = thumbLeft - (containerWidth / 2) + (thumbWidth / 2);

        container.scrollTo({
          left: scrollPosition,
          behavior: 'smooth'
        });
      }
    }
  }, [activeIndex]);

  return (
    <div className="max-w-5xl mx-auto">
      {/* Main viewer container */}
      <div
        ref={viewerRef}
        className={`relative rounded-xl overflow-hidden shadow-xl transition-all duration-300 ${
          isFullscreen ? 'bg-black' : ''
        }`}
      >
        {/* Main 360 Image Display */}
        <div className="relative">
          <img
            src={images[activeIndex].url}
            alt={images[activeIndex].title}
            className={`w-full object-cover transition-opacity duration-500 ${
              isFullscreen ? 'h-screen' : 'h-[450px]'
            }`}
          />

          {/* Gradient overlays for better visibility of controls */}
          <div className="absolute inset-x-0 top-0 h-24 bg-gradient-to-b from-black/50 to-transparent pointer-events-none"></div>
          <div className="absolute inset-x-0 bottom-0 h-24 bg-gradient-to-t from-black/50 to-transparent pointer-events-none"></div>

          {/* Location indicator */}
          <div className="absolute top-4 left-4 bg-black/40 backdrop-blur-sm px-3 py-1.5 rounded-full flex items-center">
            <i className="fas fa-map-marker-alt text-yellow-500 mr-2"></i>
            <span className="text-white text-sm font-medium">{images[activeIndex].title}</span>
          </div>

          {/* Control panel */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-3 bg-black/40 backdrop-blur-sm rounded-full px-4 py-2">
            {/* Previous button */}
            <button
              onClick={handlePrev}
              className="w-10 h-10 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-colors"
              aria-label="Previous image"
            >
              <i className="fas fa-chevron-left"></i>
            </button>

            {/* Play/Pause button */}
            <button
              onClick={toggleRotation}
              className={`w-12 h-12 rounded-full flex items-center justify-center ${
                isRotating ? 'bg-yellow-500 text-black' : 'bg-white/20 text-white'
              } transition-colors`}
              aria-label={isRotating ? "Pause rotation" : "Start rotation"}
            >
              <i className={`fas ${isRotating ? 'fa-pause' : 'fa-play'}`}></i>
            </button>

            {/* Next button */}
            <button
              onClick={handleNext}
              className="w-10 h-10 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-colors"
              aria-label="Next image"
            >
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>

          {/* Additional controls */}
          <div className="absolute top-4 right-4 flex space-x-2">
            {/* Info button */}
            <button
              onClick={toggleInfo}
              className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                showInfo ? 'bg-yellow-500 text-black' : 'bg-black/40 backdrop-blur-sm text-white hover:bg-white/20'
              }`}
              aria-label="Show information"
            >
              <i className="fas fa-info"></i>
            </button>

            {/* Fullscreen button */}
            <button
              onClick={toggleFullscreen}
              className="w-10 h-10 rounded-full flex items-center justify-center bg-black/40 backdrop-blur-sm text-white hover:bg-white/20 transition-colors"
              aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              <i className={`fas ${isFullscreen ? 'fa-compress' : 'fa-expand'}`}></i>
            </button>
          </div>

          {/* Info panel */}
          {showInfo && (
            <div className="absolute bottom-20 right-4 w-64 bg-black/70 backdrop-blur-md rounded-lg p-4 text-white">
              <h4 className="font-medium mb-2">About 360° Views</h4>
              <p className="text-sm text-gray-300 mb-3">
                Explore panoramic views of North East India. Use the controls to navigate between locations.
              </p>

              <div className="mb-3">
                <h5 className="text-xs uppercase tracking-wider text-gray-400 mb-1">Rotation Speed</h5>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => changeRotationSpeed(5)}
                    className={`px-2 py-1 text-xs rounded ${rotationSpeed === 5 ? 'bg-yellow-500 text-black' : 'bg-white/10 text-white'}`}
                  >
                    Slow
                  </button>
                  <button
                    onClick={() => changeRotationSpeed(3)}
                    className={`px-2 py-1 text-xs rounded ${rotationSpeed === 3 ? 'bg-yellow-500 text-black' : 'bg-white/10 text-white'}`}
                  >
                    Medium
                  </button>
                  <button
                    onClick={() => changeRotationSpeed(1)}
                    className={`px-2 py-1 text-xs rounded ${rotationSpeed === 1 ? 'bg-yellow-500 text-black' : 'bg-white/10 text-white'}`}
                  >
                    Fast
                  </button>
                </div>
              </div>

              <div className="text-xs text-gray-400">
                Current view: {activeIndex + 1} of {images.length}
              </div>
            </div>
          )}

          {/* Progress indicator */}
          <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex space-x-1">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => handleImageClick(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === activeIndex ? 'w-8 bg-yellow-500' : 'bg-white/50 hover:bg-white/80'
                }`}
                aria-label={`Go to image ${index + 1}`}
              ></button>
            ))}
          </div>
        </div>

        {/* Thumbnail Navigation */}
        <div
          ref={scrollContainerRef}
          className={`bg-gray-900 p-4 overflow-x-auto scrollbar-hide transition-opacity duration-300 ${
            isFullscreen ? 'opacity-0 pointer-events-none' : 'opacity-100'
          }`}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onMouseMove={handleMouseMove}
        >
          <div className="flex space-x-4">
            {images.map((image, index) => (
              <div
                key={index}
                onClick={() => handleImageClick(index)}
                className={`thumb-${index} cursor-pointer transition-all duration-300 ${
                  index === activeIndex
                    ? 'opacity-100 scale-110 z-10'
                    : 'opacity-60 hover:opacity-80'
                }`}
              >
                <div className="relative w-20 h-20 overflow-hidden rounded-lg">
                  <img
                    src={image.url}
                    alt={image.title}
                    className="w-full h-full object-cover"
                  />
                  {index === activeIndex && (
                    <div className="absolute inset-0 border-2 border-yellow-500 rounded-lg"></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Image360Viewer;
