import { useState, useEffect, useRef } from 'react';

/**
 * OptimizedImage Component
 * 
 * A component for optimized image loading with:
 * - Lazy loading
 * - Responsive image sizes
 * - Blur-up loading effect
 * - WebP format support check
 * 
 * @param {Object} props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Alt text for the image
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.sizes - Responsive sizes (e.g., {sm: '300px', md: '500px', lg: '800px'})
 * @param {string} props.fallbackSrc - Fallback image source if main source fails
 * @param {string} props.placeholderColor - Background color to show while loading
 * @param {boolean} props.priority - Whether this is a priority image (LCP)
 * @param {Function} props.onLoad - Callback when image is loaded
 * @returns {JSX.Element}
 */
const OptimizedImage = ({
  src,
  alt,
  className = '',
  sizes = null,
  fallbackSrc = '',
  placeholderColor = '#f3f4f6',
  priority = false,
  onLoad = () => {},
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef(null);
  const [supportsWebP, setSupportsWebP] = useState(true);

  // Check WebP support on mount
  useEffect(() => {
    const checkWebPSupport = async () => {
      try {
        const webPCheck = new Image();
        webPCheck.onload = () => setSupportsWebP(true);
        webPCheck.onerror = () => setSupportsWebP(false);
        webPCheck.src = 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==';
      } catch (e) {
        setSupportsWebP(false);
      }
    };
    
    checkWebPSupport();
  }, []);

  // Set up Intersection Observer for lazy loading
  useEffect(() => {
    if (priority) {
      // Don't use lazy loading for priority images
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && imgRef.current) {
            // When image comes into view, set the src attribute
            imgRef.current.src = src;
            observer.unobserve(entry.target);
          }
        });
      },
      { rootMargin: '200px 0px' } // Start loading when image is 200px from viewport
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (imgRef.current) {
        observer.unobserve(imgRef.current);
      }
    };
  }, [src, priority]);

  // Handle image load event
  const handleImageLoad = () => {
    setIsLoaded(true);
    onLoad();
  };

  // Handle image error
  const handleImageError = () => {
    setError(true);
    if (fallbackSrc) {
      imgRef.current.src = fallbackSrc;
    }
  };

  // Convert WebP to fallback format if not supported
  const getImageSrc = () => {
    if (!supportsWebP && src.endsWith('.webp')) {
      // Try to use JPG or PNG fallback
      return src.replace('.webp', '.jpg') || src.replace('.webp', '.png') || src;
    }
    return src;
  };

  // Generate srcSet if sizes are provided
  const generateSrcSet = () => {
    if (!sizes) return undefined;
    
    const baseSrc = getImageSrc();
    const srcSetParts = [];
    
    // Extract base path and extension
    const lastDot = baseSrc.lastIndexOf('.');
    const basePath = lastDot !== -1 ? baseSrc.substring(0, lastDot) : baseSrc;
    const extension = lastDot !== -1 ? baseSrc.substring(lastDot) : '';
    
    // Generate srcset for different sizes
    Object.entries(sizes).forEach(([size, width]) => {
      srcSetParts.push(`${basePath}-${size}${extension} ${width}w`);
    });
    
    return srcSetParts.join(', ');
  };

  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{ backgroundColor: placeholderColor }}
    >
      <img
        ref={imgRef}
        src={priority ? getImageSrc() : (isLoaded ? getImageSrc() : '')}
        data-src={!priority ? getImageSrc() : undefined}
        srcSet={generateSrcSet()}
        alt={alt}
        loading={priority ? 'eager' : 'lazy'}
        onLoad={handleImageLoad}
        onError={handleImageError}
        className={`transition-opacity duration-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
        {...props}
      />
      
      {/* Show placeholder or error state */}
      {!isLoaded && (
        <div 
          className="absolute inset-0 flex items-center justify-center bg-gray-100"
          style={{ backgroundColor: placeholderColor }}
        >
          <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      
      {error && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
          <span className="text-gray-500">Image failed to load</span>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;
