import { useState, useRef, useEffect } from 'react';

const VideoSection = () => {
  const [activeVideo, setActiveVideo] = useState(1);
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef(null);

  // Using placeholder images from Unsplash
  const videos = [
    {
      id: 0,
      title: "Wildlife Encounters",
      description: "Discover the diverse wildlife in their natural habitats",
      duration: "2:30",
      thumbnail: "https://images.unsplash.com/photo-1564349683136-77e08dba1ef3?w=800&auto=format&fit=crop",
      source: "https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4",
      icon: "paw",
      color: "#047857" // emerald-700
    },
    {
      id: 1,
      title: "Scenic Landscapes",
      description: "Explore the breathtaking mountains and valleys of North East India",
      duration: "2:15",
      thumbnail: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&auto=format&fit=crop",
      source: "https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4",
      icon: "mountain",
      color: "#1d4ed8" // blue-700
    },
    {
      id: 2,
      title: "Cultural Heritage",
      description: "Discover the rich traditions and festivals of the region",
      duration: "1:45",
      thumbnail: "https://images.unsplash.com/photo-1503455637927-730bce8583c0?w=800&auto=format&fit=crop",
      source: "https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4",
      icon: "users",
      color: "#b91c1c" // red-700
    },
    {
      id: 3,
      title: "Local Cuisine",
      description: "Taste the authentic flavors of North East Indian cuisine",
      duration: "3:10",
      thumbnail: "https://images.unsplash.com/photo-1565299624946-b28f40a0ae38?w=800&auto=format&fit=crop",
      source: "https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4",
      icon: "utensils",
      color: "#a16207" // amber-700
    },
    {
      id: 4,
      title: "Adventure Activities",
      description: "Experience thrilling adventures in the mountains and rivers",
      duration: "2:45",
      thumbnail: "https://images.unsplash.com/photo-1594882645126-14020914d58d?w=800&auto=format&fit=crop",
      source: "https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4",
      icon: "hiking",
      color: "#7e22ce" // purple-700
    }
  ];

  // Auto-rotate the coverflow
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveVideo((prev) => (prev === videos.length - 1 ? 0 : prev + 1));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handlePrev = () => {
    setActiveVideo((prev) => (prev === 0 ? videos.length - 1 : prev - 1));
    if (isPlaying) {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleNext = () => {
    setActiveVideo((prev) => (prev === videos.length - 1 ? 0 : prev + 1));
    if (isPlaying) {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };

  return (
    <section className="relative py-24 bg-white overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4wMiI+PHBhdGggZD0iTTM2IDM0YzAtMi4yMS0xLjc5LTQtNC00cy00IDEuNzktNCA0IDEuNzkgNCA0IDQgNC0xLjc5IDQtNHpNMTYgNDBjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00em0wLTEyYzAtMi4yMS0xLjc5LTQtNC00cy00IDEuNzktNCA0IDEuNzkgNCA0IDQgNC0xLjc5IDQtNHptMTIgMGMwLTIuMjEtMS43OS00LTQtNHMtNCAxLjc5LTQgNCAyLjc5IDQgNCA0IDQtMS43OSA0LTR6bS0xMi0xMmMwLTIuMjEtMS43OS00LTQtNHMtNCAxLjc5LTQgNCAyLjc5IDQgNCA0IDQtMS43OSA0LTR6bTEyIDBjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00em0xMiAwYzAtMi4yMS0xLjc5LTQtNC00cy00IDEuNzktNCA0IDEuNzkgNCA0IDQgNC0xLjc5IDQtNHptLTEyIDI0YzAtMi4yMS0xLjc5LTQtNC00cy00IDEuNzktNCA0IDEuNzkgNCA0IDQgNC0xLjc5IDQtNHptMTIgMGMwLTIuMjEtMS43OS00LTQtNHMtNCAxLjc5LTQgNCAyLjc5IDQgNCA0IDQtMS43OSA0LTR6bTEyIDBjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00em0wLTEyYzAtMi4yMS0xLjc5LTQtNC00cy00IDEuNzktNCA0IDEuNzkgNCA0IDQgNC0xLjc5IDQtNHptMC0xMmMwLTIuMjEtMS43OS00LTQtNHMtNCAxLjc5LTQgNCAyLjc5IDQgNCA0IDQtMS43OSA0LTR6bTEyIDBjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00em0wIDEyYzAtMi4yMS0xLjc5LTQtNC00cy00IDEuNzktNCA0IDEuNzkgNCA0IDQgNC0xLjc5IDQtNHptMCAxMmMwLTIuMjEtMS43OS00LTQtNHMtNCAxLjc5LTQgNCAyLjc5IDQgNCA0IDQtMS43OSA0LTR6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-50"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h4 className="text-yellow-600 text-sm uppercase tracking-widest mb-2">Visual Journey</h4>
          <h2 className="text-4xl font-bold mb-6 text-gray-900">Experience North East India</h2>
          <div className="w-24 h-1 bg-yellow-500 mx-auto mb-6"></div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Immerse yourself in the breathtaking landscapes and rich cultural heritage of North East India through our curated visual journey.
          </p>
        </div>

        {/* Rolodex/Stack Coverflow Video Cards */}
        <div className="flex flex-col md:flex-row gap-8 mb-16">
          {/* Left side - Main video display */}
          <div className="md:w-2/3">
            <div className="relative rounded-xl overflow-hidden shadow-xl">
              {/* Main video thumbnail */}
              <img
                src={videos[activeVideo].thumbnail}
                alt={videos[activeVideo].title}
                className="w-full h-[400px] object-cover"
              />

              {/* Overlay with gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-black via-black/40 to-transparent"></div>

              {/* Content overlay */}
              <div className="absolute inset-0 flex flex-col justify-between p-8 text-white">
                {/* Top section */}
                <div className="flex justify-between items-start">
                  <div className="px-3 py-1 rounded-full text-sm font-medium" style={{ backgroundColor: videos[activeVideo].color }}>
                    {videos[activeVideo].duration}
                  </div>
                </div>

                {/* Bottom section */}
                <div>
                  <h3 className="text-3xl font-bold mb-2">{videos[activeVideo].title}</h3>
                  <p className="text-white/80 mb-6">{videos[activeVideo].description}</p>

                  <button
                    onClick={handlePlayPause}
                    className="inline-flex items-center px-6 py-3 rounded-lg text-white font-medium transition-all duration-300"
                    style={{ backgroundColor: videos[activeVideo].color }}
                  >
                    <i className="fas fa-play mr-2"></i>
                    Watch Video
                  </button>
                </div>
              </div>

              {/* Hidden video element */}
              <video
                id={`video-${videos[activeVideo].id}`}
                className="hidden"
                poster={videos[activeVideo].thumbnail}
                onEnded={() => setIsPlaying(false)}
                ref={videoRef}
              >
                <source src={videos[activeVideo].source} type="video/mp4" />
              </video>
            </div>
          </div>

          {/* Right side - Stacked cards */}
          <div className="md:w-1/3">
            <h3 className="text-xl font-bold text-gray-900 mb-6">More Videos</h3>

            <div className="relative h-[400px] overflow-hidden">
              {videos.map((video, index) => {
                // Calculate position relative to active card
                const position = index - activeVideo;

                // Calculate transformations for stacked cards
                let translateY = position * 60; // Vertical offset
                let translateX = Math.abs(position) * 5; // Reduced horizontal shift to prevent overflow
                let rotate = position * 3; // Reduced rotation to prevent overflow
                let scale = 1 - Math.abs(position) * 0.05; // Subtle scaling
                let opacity = 1 - Math.abs(position) * 0.15; // Fade out distant cards
                let zIndex = 10 - Math.abs(position);

                // Hide cards that are too far from center
                if (Math.abs(position) > 3) {
                  opacity = 0;
                }

                return (
                  <div
                    key={video.id}
                    className={`absolute w-[95%] transition-all duration-500 rounded-lg overflow-hidden shadow-md cursor-pointer ${
                      index === activeVideo ? 'border-2' : 'border'
                    }`}
                    style={{
                      transform: `translateY(${translateY}px) translateX(${translateX}px) rotate(${rotate}deg) scale(${scale})`,
                      opacity: opacity,
                      zIndex: zIndex,
                      borderColor: index === activeVideo ? video.color : 'transparent',
                      backgroundColor: 'white'
                    }}
                    onClick={() => setActiveVideo(index)}
                  >
                    <div className="flex items-center p-3">
                      {/* Thumbnail */}
                      <div className="w-16 h-16 flex-shrink-0 rounded overflow-hidden mr-3">
                        <img
                          src={video.thumbnail}
                          alt={video.title}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">{video.title}</h4>
                        <p className="text-xs text-gray-500 line-clamp-2">{video.description}</p>
                        <div className="flex items-center mt-1">
                          <div className="w-5 h-5 rounded-full flex items-center justify-center mr-1" style={{ backgroundColor: `${video.color}20` }}>
                            <i className={`fas fa-${video.icon} text-[10px]`} style={{ color: video.color }}></i>
                          </div>
                          <span className="text-xs text-gray-400">{video.duration}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Navigation Controls - Moved outside the card stack area */}
            <div className="mt-12 flex flex-col items-center">
              {/* Stylish navigation buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handlePrev}
                  className="w-12 h-12 flex items-center justify-center shadow-md rounded-lg bg-white hover:bg-gray-50 transition-all duration-300 transform hover:-translate-y-1"
                  style={{
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08), 0 0 1px rgba(0,0,0,0.1)'
                  }}
                >
                  <i className="fas fa-chevron-up text-gray-700"></i>
                </button>

                <button
                  onClick={handleNext}
                  className="w-12 h-12 flex items-center justify-center shadow-md rounded-lg bg-white hover:bg-gray-50 transition-all duration-300 transform hover:translate-y-1"
                  style={{
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08), 0 0 1px rgba(0,0,0,0.1)'
                  }}
                >
                  <i className="fas fa-chevron-down text-gray-700"></i>
                </button>
              </div>

              {/* Video counter */}
              <div className="mt-4 text-sm text-gray-500 font-medium">
                <span className="text-gray-900">{activeVideo + 1}</span>
                <span className="mx-1">/</span>
                <span>{videos.length}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Video Player Modal - Shows when a video is playing */}
        {isPlaying && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90">
            <div className="relative w-full max-w-4xl">
              <video
                ref={videoRef}
                className="w-full h-auto"
                autoPlay
                controls
                onEnded={() => setIsPlaying(false)}
              >
                <source src={videos[activeVideo].source} type="video/mp4" />
              </video>

              <button
                onClick={() => {
                  videoRef.current.pause();
                  setIsPlaying(false);
                }}
                className="absolute top-4 right-4 bg-white text-gray-800 w-10 h-10 rounded-full flex items-center justify-center hover:bg-yellow-500 hover:text-black transition-colors duration-300 shadow-md"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          </div>
        )}

        {/* Features Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <div className="bg-gray-50 p-6 rounded-xl shadow-sm text-center border border-gray-100">
            <div className="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-mountain text-yellow-600 text-xl"></i>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Scenic Landscapes</h3>
            <p className="text-gray-600 text-sm">Breathtaking mountains, valleys, and pristine lakes await your exploration</p>
          </div>

          <div className="bg-gray-50 p-6 rounded-xl shadow-sm text-center border border-gray-100">
            <div className="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-users text-yellow-600 text-xl"></i>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Cultural Heritage</h3>
            <p className="text-gray-600 text-sm">Discover rich traditions, vibrant festivals, and the local way of life</p>
          </div>

          <div className="bg-gray-50 p-6 rounded-xl shadow-sm text-center border border-gray-100">
            <div className="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-utensils text-yellow-600 text-xl"></i>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Culinary Delights</h3>
            <p className="text-gray-600 text-sm">Taste authentic local cuisine and traditional recipes of the region</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default VideoSection;
