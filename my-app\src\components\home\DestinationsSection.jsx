import { Link } from 'react-router-dom';
import { useState, useEffect, useRef } from 'react';

// Import local images
import darjeelingImg from '../../assets/photos/dargeeling2.webp';
import gangtokImg from '../../assets/photos/city_gangtok.webp';
import northSikkimImg from '../../assets/photos/paranoma1.webp';
import natureImg from '../../assets/photos/nature2.webp';
import teaImg from '../../assets/photos/dargeeling_tea.webp';
import monksImg from '../../assets/photos/monks4.webp';

const DestinationsSection = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [startX, setStartX] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const coverflowRef = useRef(null);

  const destinations = [
    {
      id: 1,
      name: 'Darjeeling',
      image: darjeelingImg,
      secondaryImage: teaImg,
      description: 'Experience the charm of tea gardens and panoramic Himalayan views',
      highlights: ['Tiger Hill Sunrise', 'Toy Train Ride', 'Tea Gardens', 'Himalayan Mountaineering Institute'],
    },
    {
      id: 2,
      name: 'Gangtok',
      image: gangtokImg,
      secondaryImage: monksImg,
      description: 'Explore the vibrant capital city of Sikkim with its monasteries and viewpoints',
      highlights: ['Nathula Pass', 'Rumtek Monastery', 'MG Marg', 'Banjhakri Falls'],
    },
    {
      id: 3,
      name: 'North Sikkim',
      image: northSikkimImg,
      secondaryImage: northSikkimImg,
      description: 'Discover the pristine beauty of Lachen and Lachung in North Sikkim',
      highlights: ['Gurudongmar Lake', 'Yumthang Valley', 'Zero Point', 'Lachung Monastery'],
    },
    {
      id: 4,
      name: 'Pelling',
      image: natureImg,
      secondaryImage: natureImg,
      description: 'Enjoy spectacular views of Kanchenjunga and ancient monasteries',
      highlights: ['Kanchenjunga View', 'Pemayangtse Monastery', 'Rabdentse Ruins', 'Khecheopalri Lake'],
    },
    {
      id: 5,
      name: 'Dooars',
      image: teaImg, // Using tea image as a placeholder
      secondaryImage: teaImg,
      description: 'Explore the lush forests, wildlife sanctuaries and tea gardens of Dooars',
      highlights: ['Jaldapara National Park', 'Gorumara National Park', 'Buxa Tiger Reserve', 'Chapramari Wildlife Sanctuary'],
    },
  ];

  const handlePrev = () => {
    setActiveIndex((prev) => (prev === 0 ? destinations.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setActiveIndex((prev) => (prev === destinations.length - 1 ? 0 : prev + 1));
  };

  const handleMouseDown = (e) => {
    setStartX(e.pageX);
    setIsDragging(true);
  };

  const handleTouchStart = (e) => {
    setStartX(e.touches[0].clientX);
    setIsDragging(true);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const currentX = e.pageX;
    handleSwipe(currentX);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const currentX = e.touches[0].clientX;
    handleSwipe(currentX);
  };

  const handleSwipe = (currentX) => {
    if (startX === null) return;

    const diff = startX - currentX;
    if (diff > 50) {
      handleNext();
      setIsDragging(false);
      setStartX(null);
    } else if (diff < -50) {
      handlePrev();
      setIsDragging(false);
      setStartX(null);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setStartX(null);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    setStartX(null);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 5000);

    return () => clearInterval(interval);
  }, [activeIndex]);

  return (
    <section className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <div className="text-xs uppercase tracking-widest text-gray-500 mb-3">Discover</div>
          <h2 className="text-3xl font-light mb-4 text-gray-900">Popular Destinations</h2>
          <div className="w-[1px] h-16 bg-gray-200 mx-auto mb-4"></div>
          <p className="text-gray-600 max-w-xl mx-auto">
            Explore the most breathtaking locations in North East India
          </p>
        </div>

        {/* Coverflow Effect */}
        <div
          className="relative h-[550px] overflow-hidden"
          ref={coverflowRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div className="absolute inset-0 flex items-center justify-center">
            {destinations.map((destination, index) => {
              // Calculate position relative to active index
              const position = index - activeIndex;

              // Calculate z-index (higher for items closer to center)
              const zIndex = destinations.length - Math.abs(position);

              // Calculate transform properties
              let translateX = position * 60; // Horizontal offset
              let translateZ = -Math.abs(position) * 100; // Depth
              let rotateY = position * 25; // Rotation
              let scale = 1 - Math.abs(position) * 0.2; // Scale
              let opacity = 1 - Math.abs(position) * 0.3; // Opacity

              return (
                <div
                  key={destination.id}
                  className="absolute w-[340px] h-[500px] transition-all duration-500 bg-white overflow-hidden"
                  style={{
                    transform: `translateX(${translateX}%) translateZ(${translateZ}px) rotateY(${rotateY}deg) scale(${scale})`,
                    opacity: opacity,
                    zIndex: zIndex,
                    boxShadow: index === activeIndex ? '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
                  }}
                  onClick={() => setActiveIndex(index)}
                >
                  <div className="h-[250px] overflow-hidden relative">
                    <img
                      src={destination.image}
                      alt={destination.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-0 left-0 bg-indigo-500 text-white text-xs tracking-wider py-1 px-3">
                      FEATURED
                    </div>
                  </div>

                  <div className="p-8">
                    <div className="mb-2 text-xs text-gray-500 uppercase tracking-wider">North East India</div>
                    <h3 className="text-2xl font-light text-gray-900 mb-4 border-b border-gray-100 pb-4">{destination.name}</h3>

                    <p className="text-gray-600 mb-6 text-sm leading-relaxed">
                      {destination.description}
                    </p>

                    <div className="mb-6">
                      <div className="text-xs uppercase tracking-wider text-gray-500 mb-3">Highlights</div>
                      <div className="flex flex-wrap gap-2">
                        {destination.highlights.slice(0, 3).map((highlight, idx) => (
                          <span key={idx} className="inline-block bg-gray-50 border border-gray-100 px-2 py-1 text-xs text-gray-700">
                            {highlight}
                          </span>
                        ))}
                      </div>
                    </div>

                    <Link
                      to="/plan"
                      className="inline-block border border-gray-900 text-gray-900 px-6 py-2 text-sm uppercase tracking-wider hover:bg-gray-900 hover:text-white transition-colors duration-300"
                    >
                      Discover
                    </Link>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Navigation Controls */}
          <button
            onClick={handlePrev}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white border border-gray-200 text-gray-800 w-10 h-10 flex items-center justify-center z-50 hover:bg-gray-900 hover:text-white hover:border-gray-900 transition-colors duration-300 shadow-sm"
          >
            <i className="fas fa-chevron-left"></i>
          </button>

          <button
            onClick={handleNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white border border-gray-200 text-gray-800 w-10 h-10 flex items-center justify-center z-50 hover:bg-gray-900 hover:text-white hover:border-gray-900 transition-colors duration-300 shadow-sm"
          >
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>

        <div className="text-center mt-16">
          <Link
            to="/plan"
            className="inline-block border border-gray-900 px-8 py-3 text-sm uppercase tracking-widest text-gray-900 hover:bg-gray-900 hover:text-white transition-colors duration-300"
          >
            View All Destinations
          </Link>
        </div>
      </div>
    </section>
  );
};

export default DestinationsSection;
