import { useEffect, useRef, useState } from 'react';

/**
 * Custom hook for lazy loading elements when they enter the viewport
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.rootMargin - Root margin for IntersectionObserver (default: '200px 0px')
 * @param {number} options.threshold - Threshold for IntersectionObserver (default: 0)
 * @param {boolean} options.triggerOnce - Whether to trigger only once (default: true)
 * @returns {Object} - Object containing ref and isVisible state
 */
const useLazyLoad = (options = {}) => {
  const {
    rootMargin = '200px 0px',
    threshold = 0,
    triggerOnce = true
  } = options;
  
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef(null);
  const observerRef = useRef(null);
  
  useEffect(() => {
    // Create observer instance
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        
        if (entry.isIntersecting) {
          setIsVisible(true);
          
          // Unobserve if triggerOnce is true
          if (triggerOnce && elementRef.current) {
            observerRef.current.unobserve(elementRef.current);
          }
        } else if (!triggerOnce) {
          setIsVisible(false);
        }
      },
      { rootMargin, threshold }
    );
    
    return () => {
      if (observerRef.current && elementRef.current) {
        observerRef.current.unobserve(elementRef.current);
      }
    };
  }, [rootMargin, threshold, triggerOnce]);
  
  useEffect(() => {
    const currentElement = elementRef.current;
    const currentObserver = observerRef.current;
    
    if (currentElement && currentObserver) {
      currentObserver.observe(currentElement);
    }
    
    return () => {
      if (currentElement && currentObserver) {
        currentObserver.unobserve(currentElement);
      }
    };
  }, [elementRef.current]);
  
  return { ref: elementRef, isVisible };
};

export default useLazyLoad;
