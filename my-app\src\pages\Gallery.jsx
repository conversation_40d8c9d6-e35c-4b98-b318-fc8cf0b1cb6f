import React, { useState, useEffect } from 'react';
import SEO from '../components/common/SEO';
import AOS from 'aos';
import 'aos/dist/aos.css';

// Images
import darjeeling from '../assets/photos/dargeeling2.webp';
import plan2 from '../assets/photos/plan2.png';
import wildlife_redpanda from '../assets/photos/wildlife_redpanda.webp';
import wildlife_snowleopard from '../assets/photos/wildlife_snowleopard.webp';
import wildlife_yak from '../assets/photos/wildlife_yak.webp';
import nature2 from '../assets/photos/nature2.webp';
import nature4 from '../assets/photos/nature4.webp';
import culture1 from '../assets/photos/culture1.webp';
import monks4 from '../assets/photos/monks4.webp';
import attractions1 from '../assets/photos/attractions1.webp';
import tea from '../assets/photos/tea.webp';
import city_dargeeling from '../assets/photos/city_dargeeling.webp';
import city_gangtok from '../assets/photos/city_gangtok.webp';
import dargeeling_tea from '../assets/photos/dargeeling_tea.webp';
import nature from '../assets/photos/nature.webp';

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [activeCategory, setActiveCategory] = useState('All');

  const categories = ['All', 'Nature', 'City', 'Attractions', 'Culture', 'Wildlife'];

  const images = [
    { id: 1, src: nature2, title: 'Kanchenjunga Range', category: 'Attractions' },
    { id: 2, src: city_gangtok, title: 'Gangtok City View', category: 'City' },
    { id: 3, src: plan2, title: 'North Sikkim Mountains', category: 'Nature' },
    { id: 4, src: city_dargeeling, title: 'City of Darjeeling', category: 'City' },
    { id: 5, src: culture1, title: 'Monks in Sikkim', category: 'Culture' },
    { id: 6, src: darjeeling, title: 'Darjeeling Toy Train', category: 'Attractions' },
    { id: 7, src: nature4, title: 'Lachen', category: 'Nature' },
    { id: 8, src: attractions1, title: 'Gurudongmar Lake', category: 'Attractions' },
    { id: 9, src: dargeeling_tea, title: 'Darjeeling Tea', category: 'Attractions' },
    { id: 10, src: wildlife_redpanda, title: 'Red Panda', category: 'Wildlife' },
    { id: 11, src: nature, title: 'Lachung', category: 'Nature' },
    { id: 12, src: monks4, title: 'Monks doing meditation', category: 'Culture' },
    { id: 13, src: wildlife_snowleopard, title: 'Snow Leopard', category: 'Wildlife' },
    { id: 14, src: tea, title: 'Darjeeling Tea Garden', category: 'Attractions' },
    { id: 15, src: wildlife_yak, title: 'Yak', category: 'Wildlife' },
  ];

  const filteredImages = activeCategory === 'All'
    ? images
    : images.filter(image => image.category === activeCategory);

  useEffect(() => {
    AOS.init({ duration: 200, once: false, mirror: true });
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <>
      <SEO
        title="Photo Gallery - TRYPINDIA Travel"
        description="Explore our stunning photo gallery showcasing the breathtaking landscapes, vibrant culture, and unique wildlife of North East India."
        keywords="travel gallery, north east india photos, darjeeling pictures, gangtok images, sikkim photography, wildlife photos"
        ogType="article"
        ogImage="https://your-website.com/og-gallery.jpg"
      />

      <div className="relative min-h-screen w-full bg-gray-50 overflow-hidden">
        {/* Simple Hero */}
        <section className="pt-20 pb-12 md:pt-28 md:pb-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                Photo Gallery
              </h1>
              <div className="w-20 h-1 bg-amber-500 mx-auto mb-6"></div>
              <p className="text-gray-700 font-medium mb-8">
                Discover the beauty of North East India through our collection of photographs
              </p>
            </div>

            {/* Featured Images Row */}
            <div className="grid grid-cols-3 md:grid-cols-5 gap-2 md:gap-3 max-w-4xl mx-auto">
              {images.slice(0, 5).map((image, index) => (
                <div
                  key={`featured-${image.id}`}
                  className={`overflow-hidden rounded-lg shadow-sm ${index === 2 ? 'col-span-1 md:col-span-1' : 'col-span-1'}`}
                >
                  <img
                    src={image.src}
                    alt={image.title}
                    className="w-full h-24 md:h-32 object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Filter */}
        <div className="py-8 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="flex flex-wrap justify-center gap-3">
                {categories.map(category => (
                  <button
                    key={category}
                    className={`px-5 py-2 rounded-md font-medium text-sm transition-all duration-200 ${
                      activeCategory === category
                        ? 'bg-gray-800 text-white'
                        : 'bg-white text-gray-800 hover:bg-gray-100 border border-gray-300'
                    }`}
                    onClick={() => setActiveCategory(category)}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Gallery */}
        <section className="py-12 px-4 bg-white">
          <div className="container mx-auto">
            <div className="max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
              {filteredImages.map(image => (
                <div
                  key={image.id}
                  data-aos="fade-up"
                  className="relative overflow-hidden rounded-md shadow-sm cursor-pointer group h-60 md:h-72 border border-gray-100"
                  onClick={() => setSelectedImage(image)}
                >
                  <img
                    src={image.src}
                    alt={image.title}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-0 w-full p-4 bg-white/90 backdrop-blur-sm">
                    <h2 className="font-medium text-gray-800 text-sm md:text-base">{image.title}</h2>
                    <div className="flex items-center mt-1">
                      <span className="bg-amber-100 text-amber-800 text-xs px-2 py-0.5 rounded">
                        {image.category}
                      </span>
                    </div>
                  </div>
                  <div className="absolute top-3 right-3 bg-white w-8 h-8 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-sm">
                    <i className="fas fa-search-plus text-gray-600"></i>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Lightbox */}
        {selectedImage && (
          <div
            className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div className="relative max-w-4xl max-h-[90vh] w-full">
              <img
                src={selectedImage.src}
                alt={selectedImage.title}
                className="w-full max-h-[80vh] object-contain rounded-md shadow-lg"
              />
              <div className="bg-white p-4 rounded-md mt-3">
                <h2 className="text-xl font-medium text-gray-800 mb-1">{selectedImage.title}</h2>
                <div className="flex items-center">
                  <span className="bg-amber-100 text-amber-800 text-xs px-2 py-0.5 rounded">
                    {selectedImage.category}
                  </span>
                </div>
              </div>
              <button
                className="absolute top-3 right-3 bg-white text-gray-800 w-8 h-8 rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors shadow-md"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedImage(null);
                }}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Gallery;
