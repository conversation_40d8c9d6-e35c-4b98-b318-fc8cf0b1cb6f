import { useEffect } from 'react';
import HeroSection from '../components/home/<USER>';
import DestinationsSection from '../components/home/<USER>';
import VideoSection from '../components/common/VideoSection';
import Image360Viewer from '../components/common/Image360Viewer';

// Import local images
import panorama1 from '../assets/photos/paranoma1.webp';
import darjeeling from '../assets/photos/dargeeling2.webp';
import dooars from '../assets/photos/dooars.webp';
import nature2 from '../assets/photos/nature2.webp';
import city_gangtok from '../assets/photos/city_gangtok.webp';

const Home = () => {
  useEffect(() => {
    // Initialize AOS (Animate on Scroll) if it's being used
    if (typeof AOS !== 'undefined') {
      AOS.init({
        duration: 1000,
        once: true,
      });
    }

    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // 360 images data
  const images360 = [
    {
      url: panorama1,
      title: 'North Sikkim Mountains'
    },
    {
      url: darjeeling,
      title: 'Darjeeling Tea Gardens'
    },
    {
      url: dooars,
      title: 'Dooars Forest'
    },
    {
      url: nature2,
      title: 'Sikkim Valley'
    },
    {
      url: city_gangtok,
      title: 'Gangtok City'
    }
  ];

  return (
    <div className="home-page">
      {/* Hero Section */}
      <HeroSection />

      {/* Destinations Section */}
      <DestinationsSection />

      {/* Video Section */}
      <VideoSection />

      {/* 360 Image Viewer Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <div className="inline-block px-3 py-1 bg-yellow-100 text-yellow-700 text-xs font-medium tracking-wider uppercase rounded-full mb-3">
              Interactive Experience
            </div>
            <h2 className="text-3xl font-bold mb-4 text-gray-900">Explore North East India in 360°</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Immerse yourself in breathtaking panoramic views of our destinations. Rotate, zoom, and experience the beauty as if you were there.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-6 md:p-8 overflow-hidden">
            <Image360Viewer images={images360} />

            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="p-4">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <i className="fas fa-sync-alt text-yellow-600"></i>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Auto-Rotation</h3>
                <p className="text-gray-600 text-sm">
                  Enable auto-rotation to sit back and enjoy a complete panoramic tour of each location.
                </p>
              </div>

              <div className="p-4">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <i className="fas fa-expand-arrows-alt text-yellow-600"></i>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Fullscreen Mode</h3>
                <p className="text-gray-600 text-sm">
                  Enter fullscreen mode for a more immersive experience of these stunning locations.
                </p>
              </div>

              <div className="p-4">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <i className="fas fa-map-marked-alt text-yellow-600"></i>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Multiple Locations</h3>
                <p className="text-gray-600 text-sm">
                  Browse through different locations to get a preview of what awaits you on your journey.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Why Choose TrypIndia</h2>
            <div className="w-20 h-1 bg-yellow-500 mx-auto my-4"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="relative overflow-hidden group">
              <div className="absolute inset-0 bg-yellow-500 transform -translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-in-out z-0"></div>
              <div className="relative z-10 p-8 border border-gray-200 h-full transition-all duration-500 group-hover:border-transparent">
                <div className="text-center mb-4">
                  <i className="fas fa-map-marked-alt text-yellow-500 text-4xl group-hover:text-white transition-colors duration-500"></i>
                </div>
                <h3 className="text-xl font-bold text-center mb-4 group-hover:text-white transition-colors duration-500">Local Expertise</h3>
                <p className="text-gray-600 text-center group-hover:text-white transition-colors duration-500">
                  Our team consists of local experts who know every hidden gem and can provide authentic insights.
                </p>
              </div>
            </div>

            <div className="relative overflow-hidden group">
              <div className="absolute inset-0 bg-yellow-500 transform -translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-in-out z-0"></div>
              <div className="relative z-10 p-8 border border-gray-200 h-full transition-all duration-500 group-hover:border-transparent">
                <div className="text-center mb-4">
                  <i className="fas fa-hand-holding-heart text-yellow-500 text-4xl group-hover:text-white transition-colors duration-500"></i>
                </div>
                <h3 className="text-xl font-bold text-center mb-4 group-hover:text-white transition-colors duration-500">Personalized Service</h3>
                <p className="text-gray-600 text-center group-hover:text-white transition-colors duration-500">
                  We tailor each journey to your preferences, ensuring a unique and memorable experience.
                </p>
              </div>
            </div>

            <div className="relative overflow-hidden group">
              <div className="absolute inset-0 bg-yellow-500 transform -translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-in-out z-0"></div>
              <div className="relative z-10 p-8 border border-gray-200 h-full transition-all duration-500 group-hover:border-transparent">
                <div className="text-center mb-4">
                  <i className="fas fa-shield-alt text-yellow-500 text-4xl group-hover:text-white transition-colors duration-500"></i>
                </div>
                <h3 className="text-xl font-bold text-center mb-4 group-hover:text-white transition-colors duration-500">Safety First</h3>
                <p className="text-gray-600 text-center group-hover:text-white transition-colors duration-500">
                  Your safety is our priority. We follow strict protocols and work with trusted partners.
                </p>
              </div>
            </div>

            <div className="relative overflow-hidden group">
              <div className="absolute inset-0 bg-yellow-500 transform -translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-in-out z-0"></div>
              <div className="relative z-10 p-8 border border-gray-200 h-full transition-all duration-500 group-hover:border-transparent">
                <div className="text-center mb-4">
                  <i className="fas fa-leaf text-yellow-500 text-4xl group-hover:text-white transition-colors duration-500"></i>
                </div>
                <h3 className="text-xl font-bold text-center mb-4 group-hover:text-white transition-colors duration-500">Responsible Tourism</h3>
                <p className="text-gray-600 text-center group-hover:text-white transition-colors duration-500">
                  We're committed to sustainable practices that respect local communities and the environment.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-24 bg-gray-900 relative">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1506461883276-594a12b11cf3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80')] bg-cover bg-center bg-no-repeat opacity-30"></div>

        <div className="max-w-5xl mx-auto px-4 relative z-10">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-1/2 mb-10 md:mb-0 text-left">
              <h2 className="text-4xl font-bold text-white mb-6">Ready to Start Your <span className="text-yellow-500">Adventure?</span></h2>
              <p className="text-gray-300 text-lg mb-8">
                Contact us today to plan your perfect North East India journey. Our team is ready to create an unforgettable experience tailored just for you.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="/plan"
                  className="inline-block bg-yellow-500 text-gray-900 px-8 py-3 font-medium border border-yellow-500 hover:bg-yellow-600 transition-all duration-300"
                >
                  Plan Your Trip
                </a>
                <a
                  href="/contact"
                  className="inline-block bg-transparent text-white px-8 py-3 font-medium border border-white hover:bg-white/10 transition-all duration-300"
                >
                  Contact Us
                </a>
              </div>
            </div>

            <div className="md:w-5/12">
              <div className="bg-white/10 backdrop-blur-sm p-8 border border-white/20">
                <h3 className="text-2xl font-bold text-white mb-4">Get in Touch</h3>
                <p className="text-gray-300 mb-6">
                  Have questions? We're here to help you plan the perfect trip.
                </p>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 flex items-center justify-center border border-yellow-500 text-yellow-500 mr-3">
                    <i className="fas fa-phone"></i>
                  </div>
                  <span className="text-white">+91 9876543210</span>
                </div>
                <div className="flex items-center">
                  <div className="w-10 h-10 flex items-center justify-center border border-yellow-500 text-yellow-500 mr-3">
                    <i className="fas fa-envelope"></i>
                  </div>
                  <span className="text-white"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
