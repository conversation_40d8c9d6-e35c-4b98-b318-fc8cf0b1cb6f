import { useState, useEffect } from 'react';
import emailjs from '@emailjs/browser';
import SEO from '../components/common/SEO';

// Lazy load the emailjs library
const initEmailJs = () => {
  emailjs.init("EpwUY5CAqo4O5Hso3");
};

const Contact = () => {
  // Initialize emailjs when component mounts
  useEffect(() => {
    initEmailJs();
  }, []);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone_number: '+91 ', // Initialize with default country code
    destination: '',
    message: ''
  });

  const [status, setStatus] = useState('');

  const destinations = [
    'Select Destination',
    'Darjeeling',
    'Darjeeling-Gangtok',
    'Gangtok-North Sikkim (Lachen, Lachung)',
    'Darjeeling-Gangtok-North Sikkim (Lachen, Lachung)',
  ];

  // Removed direct initialization - now handled in useEffect

  const handleChange = (e) => {
    if (e.target.name === 'phone_number') {
      const value = e.target.value;
      // Allow any input that starts with '+' and contains digits and spaces
      // with a reasonable maximum length
      if (value.length <= 16 &&
          (value.startsWith('+') || value === '') &&
          /^(\+\d{0,3}\s?\d*)?$/.test(value)) {
        setFormData({
          ...formData,
          phone_number: value
        });
      }
    } else {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setStatus('sending');

    const templateParams = {
      to_name: "Trypindia",
      from_name: formData.name,
      from_email: formData.email,
      phone_number: formData.phone_number,
      destination: formData.destination,
      message: formData.message,
      reply_to: formData.email,
    };

    try {
      const result = await emailjs.send(
        'service_hg60f9q',
        'template_0ofn4jc',
        templateParams
      );

      console.log('Email sent successfully:', result.text);
      setStatus('sent');

      setFormData({
        name: '',
        email: '',
        phone_number: '+91 ',
        destination: '',
        message: ''
      });

      alert('Thank you for your message! We will get back to you soon.');

    } catch (error) {
      console.error('Failed to send email:', error);
      setStatus('error');
      alert('Failed to send message. Please try again or contact us directly.');
    }
  };

  return (
    <div className="min-h-[calc(100vh-80px)] py-16 px-4 md:px-8 lg:px-12 bg-gradient-to-b from-indigo-50 to-blue-50 relative overflow-hidden">
      <SEO
        title="Contact Us - TRYPINDIA Travel"
        description="Get in touch with our team to plan your perfect trip to North East India. We'll respond within an hour!"
        keywords="contact, travel inquiry, north east india travel, darjeeling trip, gangtok vacation"
      />

      {/* Subtle background elements - with reduced animation complexity */}
      <div className="absolute w-[400px] h-[400px] bg-gradient-to-br from-indigo-200 to-blue-200 rounded-full filter blur-[100px] opacity-20 top-[-100px] left-[-100px]"></div>
      <div className="absolute w-[300px] h-[300px] bg-gradient-to-br from-amber-200 to-orange-200 rounded-full filter blur-[100px] opacity-15 bottom-[-50px] right-[-50px]"></div>

      <div className="max-w-6xl mx-auto">
        <h1 className="text-center text-3xl md:text-4xl font-bold mb-6 mt-10 text-gray-900">Get in Touch With Us</h1>
        <p className="text-center text-gray-600 mb-10 max-w-2xl mx-auto">We're here to help plan your perfect trip to North East India. Reach out to us and we'll respond within an hour.</p>

        <div className="w-full flex flex-col md:flex-row gap-8 mx-auto bg-white rounded-2xl shadow-lg overflow-hidden relative z-10 animate-fadeIn">
          {/* Left section */}
          <div className="flex-1 p-8 md:p-12 bg-gradient-to-br from-indigo-600 to-blue-700 text-white relative overflow-hidden">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 animate-fadeIn">
              Contact Information
            </h2>
            <p className="text-indigo-100 leading-relaxed mb-10 animate-fadeIn">
              We'll get back to you within 1 hour to discuss your preferences and plan a perfect trip together!
            </p>

            <div className="flex flex-col gap-6">
              <div className="flex items-center gap-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 hover:bg-white/15 animate-fadeIn">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-xl">
                  <i className="fas fa-phone"></i>
                </div>
                <div>
                  <h3 className="font-medium text-white">Call Us</h3>
                  <p className="text-indigo-100">+91 8584807189</p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 hover:bg-white/15 animate-fadeIn">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-xl">
                  <i className="fas fa-envelope"></i>
                </div>
                <div>
                  <h3 className="font-medium text-white">Email Us</h3>
                  <p className="text-indigo-100"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 hover:bg-white/15 animate-fadeIn">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-xl">
                  <i className="fas fa-map-marker-alt"></i>
                </div>
                <div>
                  <h3 className="font-medium text-white">Our Destinations</h3>
                  <p className="text-indigo-100">Darjeeling, Gangtok, North Sikkim</p>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-white/5 rounded-full -mb-16 -mr-16"></div>
            <div className="absolute top-0 right-0 w-20 h-20 bg-white/5 rounded-full -mt-10 -mr-10"></div>
          </div>

          {/* Right section */}
          <div className="flex-1 p-8 md:p-12 animate-fadeIn">
            <div className="w-full">
              <h2 className="text-2xl md:text-3xl font-bold mb-6 text-gray-800">
                Send Message
              </h2>
              <form onSubmit={handleSubmit} className="w-full">
                <div className="mb-5">
                  <label htmlFor="name" className="block text-gray-700 mb-2 text-left">Name</label>
                  <input
                    id="name"
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    placeholder="Your Name"
                    className="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:outline-none"
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="email" className="block text-gray-700 mb-2 text-left">Email</label>
                  <input
                    id="email"
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    placeholder="Your Email"
                    className="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:outline-none"
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="phone_number" className="block text-gray-700 mb-2 text-left">Phone Number</label>
                  <input
                    id="phone_number"
                    type="tel"
                    name="phone_number"
                    value={formData.phone_number}
                    onChange={handleChange}
                    required
                    placeholder="+91 1234567890"
                    pattern="^\+\d{1,3}\s?\d{10}$"
                    title="Please enter country code (e.g. +91) followed by your 10-digit phone number"
                    className="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:outline-none tracking-wider"
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="destination" className="block text-gray-700 mb-2 text-left">Destination</label>
                  <select
                    id="destination"
                    name="destination"
                    value={formData.destination}
                    onChange={handleChange}
                    required
                    className="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:outline-none appearance-none bg-no-repeat bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2020%2020%22%3E%3Cpath%20stroke%3D%22%236b7280%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20stroke-width%3D%221.5%22%20d%3D%22M6%208l4%204%204-4%22%2F%3E%3C%2Fsvg%3E')] bg-[right_0.5rem_center] bg-[length:1.5em]"
                  >
                    {destinations.map((dest, index) => (
                      <option key={index} value={index === 0 ? "" : dest} className="bg-white text-gray-800">
                        {dest}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mb-5">
                  <label htmlFor="message" className="block text-gray-700 mb-2 text-left">Message</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    placeholder="Your Message"
                    rows="4"
                    className="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:outline-none"
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className={`w-full py-3 px-6 bg-gradient-to-r from-indigo-600 to-blue-700 text-white rounded-lg text-lg font-medium transition-all duration-300 ${
                    status === "sending" ? "opacity-75 cursor-not-allowed" : ""
                  } hover:shadow-lg hover:shadow-indigo-200 active:scale-[0.98]`}
                  disabled={status === "sending"}
                >
                  {status === "sending"
                    ? "Sending..."
                    : status === "sent"
                    ? "Message Sent!"
                    : status === "error"
                    ? "Error Sending"
                    : "Send Message"}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* Removed inline styles to reduce main thread work */}
    </div>
  );
};

export default Contact;