/**
 * Utility functions for image optimization
 */

/**
 * Check if WebP format is supported by the browser
 * @returns {Promise<boolean>} - Promise that resolves to true if WebP is supported
 */
export const checkWebPSupport = () => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = () => resolve(true);
    webP.onerror = () => resolve(false);
    webP.src = 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==';
  });
};

/**
 * Get appropriate image format based on browser support
 * @param {string} src - Original image source
 * @param {boolean} supportsWebP - Whether browser supports WebP
 * @returns {string} - Appropriate image source
 */
export const getOptimizedImageSrc = (src, supportsWebP) => {
  if (!supportsWebP && src.endsWith('.webp')) {
    // Try to use JPG or PNG fallback
    return src.replace('.webp', '.jpg') || src.replace('.webp', '.png') || src;
  }
  return src;
};

/**
 * Generate srcSet for responsive images
 * @param {string} src - Base image source
 * @param {Object} sizes - Object with size names and widths
 * @param {boolean} supportsWebP - Whether browser supports WebP
 * @returns {string} - Generated srcSet string
 */
export const generateSrcSet = (src, sizes, supportsWebP) => {
  if (!sizes) return '';
  
  const baseSrc = getOptimizedImageSrc(src, supportsWebP);
  const srcSetParts = [];
  
  // Extract base path and extension
  const lastDot = baseSrc.lastIndexOf('.');
  const basePath = lastDot !== -1 ? baseSrc.substring(0, lastDot) : baseSrc;
  const extension = lastDot !== -1 ? baseSrc.substring(lastDot) : '';
  
  // Generate srcset for different sizes
  Object.entries(sizes).forEach(([size, width]) => {
    srcSetParts.push(`${basePath}-${size}${extension} ${width}w`);
  });
  
  return srcSetParts.join(', ');
};

/**
 * Generate sizes attribute for responsive images
 * @param {Object} breakpoints - Object with breakpoint names and media queries
 * @returns {string} - Generated sizes string
 */
export const generateSizes = (breakpoints) => {
  if (!breakpoints) return '100vw';
  
  const sizesParts = [];
  
  Object.entries(breakpoints).forEach(([breakpoint, size]) => {
    if (breakpoint === 'default') {
      sizesParts.push(size);
    } else {
      sizesParts.push(`(${breakpoint}) ${size}`);
    }
  });
  
  return sizesParts.join(', ');
};

/**
 * Preload critical images
 * @param {Array<string>} imageSrcs - Array of image sources to preload
 */
export const preloadCriticalImages = (imageSrcs) => {
  if (!imageSrcs || !Array.isArray(imageSrcs)) return;
  
  imageSrcs.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
};
