import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: ['@fortawesome/fontawesome-free', 'aos'],
        },
      },
    },
  },
  server: {
    port: 3000, // Use a standard port
    strictPort: false, // Allow fallback to another port if 3000 is in use
    hmr: true, // Use default HMR settings
    watch: {
      usePolling: false, // Disable polling for better performance
    },
  },
})
